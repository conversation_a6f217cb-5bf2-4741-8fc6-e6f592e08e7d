import { Box, Flex, useColorMode, Heading } from '@chakra-ui/react';
import { useEffect } from 'react';
import kpiService from '../../api/service/kpi/index';
import DateRangeSelect from '../dashboard/components/date-select';
import { useApiQuery } from '../../hooks/react-query-hooks';
import { Keys, LocalStorageService } from '../../utils/local-storage';
import { useAppSelector } from '../../store/store';
import './dashboard.scss';
import TooltipIcon from '../../components/info-icon-content/tooltip-message';
import { content } from '../../components/info-icon-content/info-content';
import IntegrateInfo from '../../components/integrate-info/integrate-info';
import { integrationInfoStrings } from '../../utils/strings/integrate-info-strings';
import {
   resetDateRange,
   setKpiMeta,
   setPinned,
} from '../../store/reducer/kpi-reducer';
import { useDispatch } from 'react-redux';
import DashboardDataCard from './components/DashboardDataCard';
import DashboardCardSkeleton from './DashboardCardSkeleton';
import { PayloadInterface } from './dashboard-types';

export const connectors = [
   {
      key: 'store',
      label: 'Store',
      fetchFn: (payload: PayloadInterface) =>
         kpiService.fetchDashboardData(payload),
   },
   {
      key: 'overall_metrics',
      label: 'Overall Metrics',
      fetchFn: (payload: PayloadInterface) =>
         kpiService.fetchDashboardData(payload),
   },
   {
      key: 'facebookads',
      label: 'Facebook Ads',
      fetchFn: (payload: PayloadInterface) =>
         kpiService.fetchDashboardData(payload),
   },
   {
      key: 'amazon_ads',
      label: 'Amazon Ads',
      fetchFn: (payload: PayloadInterface) =>
         kpiService.fetchDashboardData(payload),
   },
   {
      key: 'googleads',
      label: 'Google Ads',
      fetchFn: (payload: PayloadInterface) =>
         kpiService.fetchDashboardData(payload),
   },
   {
      key: 'amazon_selling_partner',
      label: 'Amazon Selling Partner',
      fetchFn: (payload: PayloadInterface) =>
         kpiService.fetchDashboardData(payload),
   },
   {
      key: 'web',
      label: 'Website',
      fetchFn: (payload: PayloadInterface) =>
         kpiService.fetchDashboardData(payload),
   },
];

function Dashboard() {
   const { dateRange, prevRange, kpiMeta, pinnedKpi } = useAppSelector(
      (state) => state.kpi,
   );
   const { optimisationsStatus } = useAppSelector((state) => state.onboarding);
   const dispatch = useDispatch();
   const { colorMode } = useColorMode();

   useEffect(() => {
      dispatch(resetDateRange());
   }, [dispatch]);

   if (
      !optimisationsStatus.complete &&
      !optimisationsStatus.channels_marketplace &&
      !optimisationsStatus.flable_pixel &&
      !optimisationsStatus.ads_account
   ) {
      return (
         <IntegrateInfo
            feature={integrationInfoStrings.dashboard.title}
            text={integrationInfoStrings.dashboard.description}
         />
      );
   }

   const { data: metaData, isFetching: metaLoad } = useApiQuery({
      queryKey: [
         'metadata',
         dateRange.start,
         dateRange.end,
         prevRange.start,
         prevRange.end,
      ],
      queryFn: () =>
         kpiService.getKpiMeta(
            LocalStorageService.getItem(Keys.ClientId) as string,
         ),
      enabled: true,
      refetchOnWindowFocus: false,
   });

   const pinnedKpis = (metaData || [])
      .filter((item) => item.pinned)
      .map((item) => ({
         kpi: item.kpi,
         category: item.category,
      }));

   const pinnedPayload = {
      clientId: LocalStorageService.getItem(Keys.ClientId) as string,
      pinnedKpis,
      startDate: dateRange.start,
      endDate: dateRange.end,
      prevStartDate: prevRange.start,
      prevEndDate: prevRange.end,
   };

   const { data: pinnedData, isFetching: pinnedDataLoading } = useApiQuery({
      queryKey: [
         'pinnedData',
         dateRange.start,
         dateRange.end,
         prevRange.start,
         prevRange.end,
      ],
      queryFn: () => kpiService.fetchPinnedData(pinnedPayload),
      enabled: !!metaData,
      refetchOnWindowFocus: false,
   });

   const buildPayload = (category: string) => ({
      clientId: LocalStorageService.getItem(Keys.ClientId) as string,
      category,
      startDate: dateRange.start,
      endDate: dateRange.end,
      prevStartDate: prevRange.start,
      prevEndDate: prevRange.end,
   });

   const connectorQueries = connectors.map((connector) => {
      const query = useApiQuery({
         queryKey: [
            'dashboard',
            connector.key,
            dateRange.start,
            dateRange.end,
            prevRange.start,
            prevRange.end,
         ],
         queryFn: () => connector.fetchFn(buildPayload(connector.key)),
         enabled: !!pinnedData,
         refetchOnWindowFocus: false,
      });
      return { ...query, connector };
   });

   useEffect(() => {
      if (pinnedData) {
         dispatch(setPinned(pinnedData.data || []));
      }
   }, [pinnedData]);

   useEffect(() => {
      if (metaData) {
         dispatch(setKpiMeta(metaData));
      }
   }, [metaData]);

   return (
      <Box
         p={4}
         backgroundColor={
            colorMode === 'dark' ? 'var(--background)' : '#fafafa'
         }
         height={'calc(100vh - 48px)'}
         overflow={'auto'}
      >
         <Flex direction={'column'} gap={5}>
            <Flex justifyContent={'space-between'}>
               <Flex alignItems='center'>
                  <Heading
                     fontSize={24}
                     size={'xl'}
                     fontWeight={600}
                     color={colorMode === 'dark' ? 'white' : 'inherit'}
                  >
                     Dashboard
                  </Heading>
                  <TooltipIcon
                     label={content.dashboard}
                     fontSize='small'
                     iconColor='#437EEB'
                     ml={2}
                     mt={1}
                  />
               </Flex>
               <DateRangeSelect dateId='dateId' rangeId='rangeId' />
            </Flex>

            <Flex direction='column' gap={5}>
               {(() => {
                  const isLoading = metaLoad || pinnedDataLoading;

                  if (isLoading) {
                     return <DashboardCardSkeleton label='Pinned' />;
                  }

                  if (!pinnedData || !pinnedKpi) {
                     return null;
                  }

                  return (
                     <DashboardDataCard
                        keyValue='pinned'
                        label='Pinned'
                        metaData={kpiMeta || []}
                        data={pinnedKpi}
                        connectorKey='pinned'
                     />
                  );
               })()}

               {connectorQueries.map(({ data, isFetching, connector }) => {
                  const isLoading = metaLoad || isFetching || pinnedDataLoading;

                  if (isLoading) {
                     return (
                        <DashboardCardSkeleton
                           key={connector.key}
                           label={connector.label}
                        />
                     );
                  }

                  if (!data?.data[connector.key]) {
                     return null;
                  }
                  return (
                     <DashboardDataCard
                        keyValue={connector.key}
                        label={connector.label}
                        metaData={kpiMeta || []}
                        data={data.data}
                        connectorKey={connector.key}
                        anomalies={data.anomaly && data.anomaly}
                     />
                  );
               })}
            </Flex>
         </Flex>
      </Box>
   );
}

export default Dashboard;
