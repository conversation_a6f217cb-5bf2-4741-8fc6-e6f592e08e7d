import FivetranConnectorWrapper from './fivetran-connector-wrapper';
import image from '../images/integrations/zoho.png';
import { connectToZohoSentiment } from '../utils';

 const  Zoho=()=>{
   return(
      <FivetranConnectorWrapper
         imageSrc={image}
         heading='Zoho CRM'
         channelType='ZOHO'
         connectToSentimentFn={connectToZohoSentiment}
         modalData={{
            heading: 'Connect to Zoho CRM ',
            content:
               'You are being redirected to Zoho to connect your account...',
         }}
      />
   );
}
export default Zoho;
