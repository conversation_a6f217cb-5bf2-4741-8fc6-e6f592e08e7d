import { createBrowserRouter, Navigate } from 'react-router-dom';
import { ModalManager } from './components';

import AppLayout from './layouts/app-layout';
import AuthLayout from './layouts/auth-layout';

/** MARCO **/
import { MarcoLayout, AnalyticsAgent } from './pages/marco';

import {
   SocialListening,
   SocialWatch,
   ContentCalender,
   Pulse,
   Login,
   Performance,
   Overview,
   Settings,
   Register,
} from './pages';
import {
   FacebookAdsForm,
   ShopifyForm,
   WoocommerceForm,
   UnicommerceForm,
   IThinkLogisticsForm,
   ShipRocketForm,
} from './pages/social-listening/components';
import WhatsappForm from './pages/social-listening/components/whatsapp-form';
import SuspenseLoader from './layouts/suspense-loader';
import Errorpage from './pages/pulse/performance-insights/error';
import Tracked from './pages/pulse/performance-insights/tracked';
import <PERSON>rror<PERSON>andler from './errorpage/error-handler';
import WebInsightTracked from './pages/pulse/web-insight-tracked';
import Onboarding from './pages/onboarding/onboarding';
import EmailVerification from './pages/auth/email-verification';
import ForgotPassword from './pages/auth/forgot-password';
import Optimisations from './pages/onboarding/components/optimisations/optimisations';
import UsersPage from './pages/user-management/users-page';
import AddEditUserPage from './pages/user-management/add-edit-user-page';
import SetPassword from './pages/auth/set-password';
import ChooseProfilePage from './pages/auth/choose-profile';
import MetaAdsManager from './pages/marco/agents/meta-ads-manager';
import MetaAdsManagerAuto from './pages/marco/agents/meta-ads-manager-auto';
import MetaAttribution from './pages/settings/attributions/meta-attribution';
import GoogleAttribution from './pages/settings/attributions/google-attribution';
import AlertTemplate from './pages/marco/custom-alerts/alert-template';
import ViewCustomAlerts from './pages/marco/custom-alerts/view-custom-alerts';
import AnalyticsBookmarks from './pages/marco/components/cards/analytics-bookmarks';
import Dashboard2 from './pages/dashboard/dashboard';
import Dashboard from './pages/dash/dashboard';

const AppRootLayout = () => {
   return (
      <>
         <ModalManager />
         <AppLayout />
      </>
   );
};

const AuthRootLayout = () => {
   return (
      <>
         <ModalManager />
         <AuthLayout />
      </>
   );
};

const OnboardingRootLayout = () => {
   return (
      <>
         <ModalManager />
         <Onboarding />
      </>
   );
};

export const router = createBrowserRouter([
   {
      path: '/',
      element: <AppRootLayout />,
      children: [
         {
            index: true,
            element: <Navigate to='marco' />,
         },
         {
            path: 'marco',
            element: (
               <SuspenseLoader>
                  <ErrorHandler>
                     <MarcoLayout />
                  </ErrorHandler>
               </SuspenseLoader>
            ),
            children: [
               {
                  path: 'analytics-agent',
                  element: (
                     <SuspenseLoader>
                        <ErrorHandler>
                           <AnalyticsAgent />
                        </ErrorHandler>
                     </SuspenseLoader>
                  ),
               },
               {
                  path: 'meta-ads-manager-auto',
                  element: (
                     <SuspenseLoader>
                        <ErrorHandler>
                           <MetaAdsManagerAuto />
                        </ErrorHandler>
                     </SuspenseLoader>
                  ),
               },
               {
                  path: 'meta-ads-manager-agent',
                  element: (
                     <SuspenseLoader>
                        <ErrorHandler>
                           <MetaAdsManager />
                        </ErrorHandler>
                     </SuspenseLoader>
                  ),
               },
            ],
         },
         {
            path: 'dashboard',
            element: (
               <SuspenseLoader>
                  <ErrorHandler>
                     <Dashboard2 />
                  </ErrorHandler>
               </SuspenseLoader>
            ),
         },
         {
            path: 'dashboard2',
            element: (
               <SuspenseLoader>
                  <ErrorHandler>
                     <Dashboard />
                  </ErrorHandler>
               </SuspenseLoader>
            ),
         },
         {
            path: 'pulse',
            element: (
               <SuspenseLoader>
                  <ErrorHandler>
                     <Performance />
                  </ErrorHandler>
               </SuspenseLoader>
            ),
         },
         {
            path: 'pulse/web-insights',
            element: <Navigate to='overview' />,
         },
         {
            path: 'pulse/web-insights/overview',
            element: (
               <SuspenseLoader>
                  <ErrorHandler>
                     <Pulse />
                  </ErrorHandler>
               </SuspenseLoader>
            ),
         },
         {
            path: 'pulse/web-insights/tracked',
            element: (
               <SuspenseLoader>
                  <ErrorHandler>
                     <WebInsightTracked />
                  </ErrorHandler>
               </SuspenseLoader>
            ),
         },
         {
            path: 'pulse/performance-insights',
            element: (
               <SuspenseLoader>
                  <ErrorHandler>
                     <Performance />
                  </ErrorHandler>
               </SuspenseLoader>
            ),
         },
         {
            path: 'pulse/performance-insights/error',
            element: (
               <SuspenseLoader>
                  <ErrorHandler>
                     <Errorpage />
                  </ErrorHandler>
               </SuspenseLoader>
            ),
         },
         {
            path: 'pulse/performance-insights/overview',
            element: (
               <SuspenseLoader>
                  <ErrorHandler>
                     <Overview />
                  </ErrorHandler>
               </SuspenseLoader>
            ),
         },
         {
            path: 'pulse/performance-insights/tracked',
            element: (
               <SuspenseLoader>
                  <ErrorHandler>
                     <Tracked />
                  </ErrorHandler>
               </SuspenseLoader>
            ),
         },
         {
            path: 'socialwatch',
            element: (
               <SuspenseLoader>
                  <ErrorHandler>
                     <SocialWatch />
                  </ErrorHandler>
               </SuspenseLoader>
            ),
         },
         {
            path: 'contentcalender',
            element: (
               <SuspenseLoader>
                  <ErrorHandler>
                     <ContentCalender />
                  </ErrorHandler>
               </SuspenseLoader>
            ),
         },
         {
            path: '/bookmarks',
            element: (
               <SuspenseLoader>
                  <ErrorHandler>
                     <AnalyticsBookmarks />
                  </ErrorHandler>
               </SuspenseLoader>
            ),
         },
         {
            path: 'alerts/create',
            element: (
               <SuspenseLoader>
                  <ErrorHandler>
                     <AlertTemplate />
                  </ErrorHandler>
               </SuspenseLoader>
            ),
         },
         {
            path: 'alerts',
            element: (
               <SuspenseLoader>
                  <ErrorHandler>
                     <ViewCustomAlerts />
                  </ErrorHandler>
               </SuspenseLoader>
            ),
         },
         {
            path: 'integrations',
            element: (
               <SuspenseLoader>
                  <ErrorHandler>
                     <SocialListening />
                  </ErrorHandler>
               </SuspenseLoader>
            ),
         },
         {
            path: 'integrations/woocommerce',
            element: (
               <SuspenseLoader>
                  <ErrorHandler>
                     <WoocommerceForm />
                  </ErrorHandler>
               </SuspenseLoader>
            ),
         },
         {
            path: 'integrations/shopify',
            element: (
               <SuspenseLoader>
                  <ErrorHandler>
                     <ShopifyForm />
                  </ErrorHandler>
               </SuspenseLoader>
            ),
         },
         {
            path: 'integrations/unicommerce',
            element: (
               <SuspenseLoader>
                  <ErrorHandler>
                     <UnicommerceForm />
                  </ErrorHandler>
               </SuspenseLoader>
            ),
         },
         {
            path: 'integrations/whatsapp',
            element: (
               <SuspenseLoader>
                  <ErrorHandler>
                     <WhatsappForm />
                  </ErrorHandler>
               </SuspenseLoader>
            ),
         },
         {
            path: 'integrations/facebook-ads',
            element: (
               <SuspenseLoader>
                  <ErrorHandler>
                     <FacebookAdsForm />
                  </ErrorHandler>
               </SuspenseLoader>
            ),
         },
         {
            path: 'integrations/ithinklogistics',
            element: (
               <SuspenseLoader>
                  <ErrorHandler>
                     <IThinkLogisticsForm />
                  </ErrorHandler>
               </SuspenseLoader>
            ),
         },
         {
            path: 'integrations/shiprocket',
            element: (
               <SuspenseLoader>
                  <ErrorHandler>
                     <ShipRocketForm />
                  </ErrorHandler>
               </SuspenseLoader>
            ),
         },
         {
            path: 'settings/',
            element: (
               <SuspenseLoader>
                  <ErrorHandler>
                     <Settings />
                  </ErrorHandler>
               </SuspenseLoader>
            ),
         },

         {
            path: 'settings/attribution-settings/google-ads',
            element: (
               <SuspenseLoader>
                  <ErrorHandler>
                     <GoogleAttribution />
                  </ErrorHandler>
               </SuspenseLoader>
            ),
         },

         {
            path: 'settings/attribution-settings/meta-ads',
            element: (
               <SuspenseLoader>
                  <ErrorHandler>
                     <MetaAttribution />
                  </ErrorHandler>
               </SuspenseLoader>
            ),
         },

         {
            path: 'optimisations',
            element: (
               <SuspenseLoader>
                  <ErrorHandler>
                     <Optimisations />
                  </ErrorHandler>
               </SuspenseLoader>
            ),
         },
         {
            path: 'user/manage-users',
            element: (
               <SuspenseLoader>
                  <ErrorHandler>
                     <UsersPage />
                  </ErrorHandler>
               </SuspenseLoader>
            ),
         },
         {
            path: 'user/add',
            element: (
               <SuspenseLoader>
                  <ErrorHandler>
                     <AddEditUserPage />
                  </ErrorHandler>
               </SuspenseLoader>
            ),
         },
         {
            path: 'user/edit',
            element: (
               <SuspenseLoader>
                  <ErrorHandler>
                     <AddEditUserPage />
                  </ErrorHandler>
               </SuspenseLoader>
            ),
         },
      ],
   },
   {
      path: 'auth',
      element: <AuthRootLayout />,
      children: [
         {
            path: 'login',
            element: (
               <SuspenseLoader>
                  <ErrorHandler>
                     <Login />
                  </ErrorHandler>
               </SuspenseLoader>
            ),
         },
         {
            path: 'register',
            element: (
               <SuspenseLoader>
                  <ErrorHandler>
                     <Register />
                  </ErrorHandler>
               </SuspenseLoader>
            ),
         },
         {
            path: 'email-verification',
            element: (
               <SuspenseLoader>
                  <ErrorHandler>
                     <EmailVerification />
                  </ErrorHandler>
               </SuspenseLoader>
            ),
         },
         {
            path: 'forgot-password',
            element: (
               <SuspenseLoader>
                  <ErrorHandler>
                     <ForgotPassword />
                  </ErrorHandler>
               </SuspenseLoader>
            ),
         },
         {
            path: 'set-password',
            element: (
               <SuspenseLoader>
                  <ErrorHandler>
                     <SetPassword />
                  </ErrorHandler>
               </SuspenseLoader>
            ),
         },
         {
            path: 'choose-profile',
            element: (
               <SuspenseLoader>
                  <ErrorHandler>
                     <ChooseProfilePage />
                  </ErrorHandler>
               </SuspenseLoader>
            ),
         },
      ],
   },
   {
      path: '/onboarding',
      element: <OnboardingRootLayout />,
      children: [
         {
            path: '/onboarding/shopify',
            element: (
               <SuspenseLoader>
                  <ErrorHandler>
                     <ShopifyForm />
                  </ErrorHandler>
               </SuspenseLoader>
            ),
         },
         {
            path: '/onboarding/unicommerce',
            element: (
               <SuspenseLoader>
                  <ErrorHandler>
                     <UnicommerceForm />
                  </ErrorHandler>
               </SuspenseLoader>
            ),
         },
      ],
   },
]);
