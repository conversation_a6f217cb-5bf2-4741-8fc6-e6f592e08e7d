import { modalTypes } from '@/components/modals/modal-types';
import { openModal } from '@/store/reducer/modal-reducer';
import { Text, useColorModeValue } from '@chakra-ui/react';
import { useDispatch } from 'react-redux';
import { ChartProp } from '../dashboard-types';

function ViewDetails(props: ChartProp) {
   const { kpiDetails } = props;
   const dispatch = useDispatch();
   const linkColor = useColorModeValue('#337CDF', '#63B3ED');
   if (!kpiDetails?.current_allData?.length) return null;
   const handleViewOpen = () => {
      dispatch(
         openModal({
            modalType: modalTypes.KPI_VIEW_DETAILS2,
            modalProps: { kpiDetails },
         }),
      );
   };

   return (
      <>
         <Text
            cursor={'pointer'}
            onClick={handleViewOpen}
            fontSize={14}
            textDecoration={'underline'}
            textAlign={'left'}
            color={linkColor}
         >
            More Details
         </Text>
      </>
   );
}

export default ViewDetails;
