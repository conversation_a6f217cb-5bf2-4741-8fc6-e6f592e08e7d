import { getFormattedVal, toHHMMSS } from '@/pages/dashboard/utils/helpers';
import { noZeroKPI } from '@/utils/strings/kpi-constants';
import { useColorMode } from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import Chart from 'react-apexcharts';
import { ChartProp, KpiTimelineItem } from '../dashboard-types';
import { ApexOptions } from 'apexcharts';

function BarChart(props: ChartProp) {
   const { kpiDetails, value } = props;
   // const { groupBy } = useAppSelector((state) => state.kpi);
   const { colorMode } = useColorMode();

   const categories = (value as KpiTimelineItem[]).map(
      (x) => x.date.split(' ')[0],
   );
   const [chartData, setchartData] = useState({
      options: {
         chart: {
            id: kpiDetails.kpi_display_name,
            toolbar: {
               show: false,
            },
         },
         xaxis: {
            //type: groupBy == 'day' ? 'datetime' : 'string',
            categories: categories,
            tickAmount: 30,
            labels: {
               show: true,
               rotate: -45,
               rotateAlways: true,
               maxHeight: 150,
               style: {
                  colors: colorMode === 'dark' ? '#fff' : '#000',
               },
            },
         },

         yaxis: {
            //type: groupBy == 'day' ? 'datetime' : 'string',

            labels: {
               style: {
                  colors: colorMode === 'dark' ? '#fff' : '#000',
               },
            },
         },
         tooltip: {
            y: {
               formatter: function (value: number) {
                  if (!value && noZeroKPI.includes(kpiDetails.kpi_names))
                     return 'N/A';
                  return kpiDetails.kpi_unit == 'time'
                     ? toHHMMSS(value)
                     : getFormattedVal(Math.round(value * 100) / 100);
               },
            },
         },
         stroke: {
            curve: 'smooth',
         },
         dataLabels: {
            enabled: false,
         },
         grid: {
            show: false,
         },
      },
      series: [
         {
            name: kpiDetails.kpi_display_name,
            data: kpiDetails.current_allData.map((x) =>
               Number(x.kpi_value?.toFixed(2)),
            ),
         },
      ],
   });
   useEffect(() => {
      setchartData({
         options: {
            chart: {
               id: kpiDetails.kpi_display_name,
               toolbar: {
                  show: false,
               },
            },
            xaxis: {
               //type: groupBy == 'day' ? 'datetime' : 'string',
               categories: categories,
               tickAmount: 30,
               labels: {
                  show: true,
                  rotate: -45,
                  rotateAlways: true,
                  maxHeight: 150,
                  style: {
                     colors: colorMode === 'dark' ? '#fff' : '#000',
                  },
               },
            },

            yaxis: {
               //type: groupBy == 'day' ? 'datetime' : 'string',

               labels: {
                  style: {
                     colors: colorMode === 'dark' ? '#fff' : '#000',
                  },
               },
            },
            tooltip: {
               y: {
                  formatter: function (value: number) {
                     if (!value && noZeroKPI.includes(kpiDetails.kpi_names))
                        return 'N/A';
                     return kpiDetails.kpi_unit == 'time'
                        ? toHHMMSS(value)
                        : getFormattedVal(Math.round(value * 100) / 100);
                  },
               },
            },
            stroke: {
               curve: 'smooth',
            },
            dataLabels: {
               enabled: false,
            },
            grid: {
               show: false,
            },
         },
         series: [
            {
               name: kpiDetails.kpi_display_name,
               data: kpiDetails.current_allData.map((x: KpiTimelineItem) =>
                  Number(x.kpi_value?.toFixed(2)),
               ),
            },
         ],
      });
   }, [kpiDetails.current_allData]);

   return (
      <>
         <Chart
            options={chartData.options as ApexOptions}
            series={chartData.series}
            type='bar'
            width='100%'
            height='400px'
         />
      </>
   );
}

export default BarChart;
