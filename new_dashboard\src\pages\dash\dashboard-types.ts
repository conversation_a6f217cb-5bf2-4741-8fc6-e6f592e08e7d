// Payload for API requests
export interface PayloadInterface {
   clientId: string;
   category: string;
   startDate: string;
   endDate: string;
   prevStartDate: string;
   prevEndDate: string;
}

// Individual KPI breakdown
export interface KpiBreakdownItem {
   value: number;
   kpi_display_name: string;
   unit: string;
   category: string;
   up: boolean;
   sub_items?: BreakDown | null; // recursive breakdown
   kpi_type: string;
}

// Recursive breakdown type
export interface BreakDown {
   [key: string]: KpiBreakdownItem;
}

// Individual KPI data
export interface KpiData {
   category: string;
   kpi_names: string;
   kpi_unit: string;
   kpi_display_name: string;
   kpi_type: string;
   daily_breakdown: BreakDown | null;
   current_total_value: number;
   previous_total_value: number;
   current_allData: KpiTimelineItem[];
   previous_allData: KpiTimelineItem[];
}

export interface KpiDataWithMeta extends KpiData {
   visible: boolean;
   pinned: boolean;
   order: number;
}

// Timeline data points for a KPI
export interface KpiTimelineItem {
   kpi_value: number;
   date: string;
}

// Dashboard response
export interface DataDashboardResponse {
   success: boolean;
   message: string;
   data: Connector;
   anomaly?: Anomalies;
}

export interface Anomalies {
   [kpiName: string]: boolean | null; // KPI anomaly can be true/false/null
}

export interface Connector {
   [key: string]: ConnectorKpiData;
}

export interface ConnectorKpiData {
   [kpiName: string]: KpiData;
}

export interface KpiCardProps {
   data: KpiDataWithMeta;
}

export interface ConnectorKpiDataWithMeta {
   [kpiName: string]: KpiDataWithMeta;
}

export interface DashboardDataCardProps {
   keyValue: string;
   label: string;
   connectorKey: string;
   data: Connector;
   metaData: KPIMeta[] | undefined;
   anomalies?: Anomalies;
}

export interface KPIMeta {
   category: string;
   kpi: string;
   visible: boolean;
   pinned: boolean;
   kpi_display_name: string;
   pinned_order: number;
   visible_order: number;
}
// Optional: utility type for filtered KPIs
export type FilteredKpi = KpiData & { order?: number; pinned?: boolean };

export interface ChartProp {
   kpiDetails: KpiDataWithMeta;
   value?: KpiTimelineItem[];
   anomaly?: boolean | null;
}
