import { Button } from '@/components/ui/button';
import {
   Popover,
   PopoverContent,
   PopoverTrigger,
} from '@/components/ui/popover';
import {
   Accordion,
   AccordionContent,
   AccordionItem,
   AccordionTrigger,
} from '@/components/ui/accordion';
import { useAppDispatch, useAppSelector } from '@/store/store';
import { LuHistory } from 'react-icons/lu';
import { groupAlertChatsByDate } from '../../utils/alerting-agent/agents-helpers';
import { groupAgentChatsByDate } from '../../utils/analytics-agent/helpers';
import { groupAutoAgentChatsByDate } from '../../utils/meta-ads-manager-auto/helpers';
import {
   setCurrentSessionID as setAnalyticsSessionID,
   setCurrentPage as setAnalyticsCurrentPage,
} from '@/store/reducer/analytics-agent-reducer';
import {
   setCurrentSessionID as setAlertSessionID,
   setCurrentPage as setAlertingCurrentPage,
} from '@/store/reducer/alerting-agent-reducer';
import { setCurrentSessionID as setMetaAdsAutoSessionID } from '@/store/reducer/metaAdsAutoAgentReducer';
import { Spinner } from '@chakra-ui/react';
import { Input } from '@/components/ui/input';
import { useEffect, useState } from 'react';
import { format } from 'date-fns';
import { useFetchAlertingHistoryQuery } from '../../apis/alerting-agent-apis';
import { useFetchAnalyticsHistoryQuery } from '../../apis/analytics-agent-apis';
import { useFetchMetaAdsAutoAgentHistoryQuery } from '../../apis/meta-ads-auto-agent-apis';
import { setCurrentHistory } from '@/store/reducer/marco-reducer';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/utils';

const MODES = {
   cmo: 'Deep Analysis',
};

const MarcoHistoryPopover = () => {
   const dispatch = useAppDispatch();

   const { currentAgent, currentHistory } = useAppSelector(
      (state) => state.marco,
   );
   const { currentPage: alertingCurrentPage } = useAppSelector(
      (state) => state.alertingAgent,
   );
   const { currentPage: analyticsCurrentPage } = useAppSelector(
      (state) => state.analyticsAgent,
   );

   const [searchTerm, setSearchTerm] = useState<string>('');

   const {
      data: alertsHistoryData,
      isLoading: isAlertsHistoryLoading,
      refetch: refetchAlertsHistory,
   } = useFetchAlertingHistoryQuery();

   const {
      data: analyticsHistoryData,
      isLoading: isAnalyticsHistoryLoading,
      refetch: refetchAnalyticsHistory,
   } = useFetchAnalyticsHistoryQuery();

   const {
      data: metaAdsAutoAgentHistoryData,
      // isLoading: isMetaAdsAutoAgentLoading,
   } = useFetchMetaAdsAutoAgentHistoryQuery();

   const groupLabels: { [key: string]: string } = {
      today: 'Today',
      yesterday: 'Yesterday',
      last7Days: 'Last 7 Days',
      lastMonth: 'Last Month',
      lastYear: 'Last Year',
      older: 'Older',
   };

   const renderAlertHistory = () => {
      if (
         !currentHistory['alerting-agent'] ||
         currentHistory['alerting-agent'].length === 0
      ) {
         return (
            <div className='text-center text-gray-500'>
               No history available
            </div>
         );
      }

      const groupedData = groupAlertChatsByDate(
         currentHistory['alerting-agent'].filter((chat) =>
            chat.user_query.toLowerCase().includes(searchTerm.toLowerCase()),
         ),
      );

      const refetchingAlertsHistory = async () => {
         dispatch(setAlertingCurrentPage(alertingCurrentPage + 1));
         await refetchAlertsHistory();
      };

      return (
         <div className='space-y-6'>
            {Object.entries(groupedData).map(([key, chats]) => {
               if (chats.length === 0) return null;

               return (
                  <div key={key}>
                     <h3 className='text-lg font-bold text-gray-800 mb-2'>
                        {groupLabels[key]}
                     </h3>
                     <div className='space-y-2'>
                        {chats.map((chat) => (
                           <div
                              key={chat.chat_id}
                              onClick={() =>
                                 dispatch(setAlertSessionID(chat.session_id))
                              }
                              className='border-b py-2 hover:bg-gray-50 hover:cursor-pointer'
                           >
                              <p className='text-sm text-gray-500'>
                                 {chat.user_query ?? 'No message'}
                              </p>
                              <p className='text-right text-[12px] text-gray-500'>
                                 {format(
                                    new Date(chat.updated_at),
                                    'MMM dd hh:mma',
                                 )}
                              </p>
                           </div>
                        ))}
                     </div>
                  </div>
               );
            })}

            {isAlertsHistoryLoading ? (
               <div className='flex h-full items-center justify-center'>
                  <Spinner />
               </div>
            ) : (
               <Button
                  className='w-full'
                  variant='outline'
                  onClick={() => void refetchingAlertsHistory()}
               >
                  Load More
               </Button>
            )}
         </div>
      );
   };

   const renderAnalyticsHistory = () => {
      if (
         !currentHistory['analytics-agent'] ||
         currentHistory['analytics-agent'].length === 0
      ) {
         return (
            <div className='text-center text-gray-500'>
               No history available
            </div>
         );
      }

      const groupedData = groupAgentChatsByDate(
         currentHistory['analytics-agent'].filter(
            (chat) =>
               chat.user_query
                  .toLowerCase()
                  .includes(searchTerm.toLowerCase()) ||
               (chat?.session_name &&
                  chat?.session_name
                     .toLowerCase()
                     .includes(searchTerm.toLowerCase())),
         ),
      );

      const refetchingAnalyticsHistory = async () => {
         dispatch(setAnalyticsCurrentPage(analyticsCurrentPage + 1));
         await refetchAnalyticsHistory();
      };

      const openSelectedSession = (sessionId: string) => {
         dispatch(setAnalyticsSessionID(sessionId));
      };

      return (
         <Accordion type='multiple' value={Object.keys(groupedData)}>
            {Object.entries(groupedData).map(([key, chats]) => {
               if (chats.length === 0) return null;

               return (
                  <AccordionItem value={key} key={key}>
                     <AccordionTrigger className='text-lg font-bold text-gray-800 mb-2'>
                        {groupLabels[key]}
                     </AccordionTrigger>
                     <AccordionContent>
                        {chats.map((chat) => (
                           <div
                              key={chat.chat_id}
                              onClick={() =>
                                 openSelectedSession(chat.session_id)
                              }
                              className='border-b last:border-b-0 px-1 py-2 hover:bg-gray-50 hover:cursor-pointer'
                           >
                              <p className='text-sm text-gray-600'>
                                 {chat?.session_name ||
                                    (chat.user_query.length > 60
                                       ? `${chat.user_query.slice(0, 60)}...`
                                       : chat.user_query) ||
                                    'No message'}
                              </p>
                              <div
                                 className={cn(
                                    'flex items-center justify-between mt-1',
                                 )}
                              >
                                 <div className='flex items-center gap-1'>
                                    {chat.question_modes &&
                                       chat.question_modes.length > 0 &&
                                       chat.question_modes
                                          .filter(
                                             (mode) =>
                                                mode && mode.trim() !== '',
                                          )
                                          .map((mode) => {
                                             if (mode.includes('cmo'))
                                                return (
                                                   <Badge
                                                      key={mode}
                                                      className={`text-[12px] text-black px-2 py-0 bg-white font-semibold rounded-full`}
                                                   >
                                                      {MODES[
                                                         'cmo' as keyof typeof MODES
                                                      ] || mode}
                                                   </Badge>
                                                );
                                          })
                                          .filter(Boolean)}
                                 </div>
                                 <p className='mt-2 text-right text-[12px] text-gray-400'>
                                    {format(
                                       new Date(chat.updated_at),
                                       'MMM dd hh:mma',
                                    )}
                                 </p>
                              </div>
                           </div>
                        ))}
                     </AccordionContent>
                  </AccordionItem>
               );
            })}
            {isAnalyticsHistoryLoading ? (
               <div className='flex h-full items-center justify-center'>
                  <Spinner />
               </div>
            ) : (
               <Button
                  className='w-full'
                  variant='outline'
                  onClick={() => void refetchingAnalyticsHistory()}
               >
                  Load More
               </Button>
            )}
         </Accordion>
      );
   };

   const renderMetaAdsAutoAgentHistory = () => {
      if (
         !metaAdsAutoAgentHistoryData ||
         metaAdsAutoAgentHistoryData.length === 0
      ) {
         return (
            <div className='text-center text-gray-500'>
               No history available
            </div>
         );
      }

      const groupedData = groupAutoAgentChatsByDate(
         metaAdsAutoAgentHistoryData.filter((chat) =>
            chat.user_query.toLowerCase().includes(searchTerm.toLowerCase()),
         ),
      );

      return (
         <div className='space-y-6'>
            {Object.entries(groupedData).map(([key, chats]) => {
               if (chats.length === 0) return null;

               return (
                  <div key={key}>
                     <h3 className='text-lg font-bold text-gray-800 mb-2'>
                        {groupLabels[key]}
                     </h3>
                     <div className='space-y-2'>
                        {chats.map((chat) => (
                           <div
                              key={chat.chat_id}
                              onClick={() =>
                                 dispatch(
                                    setMetaAdsAutoSessionID(chat.session_id),
                                 )
                              }
                              className='border-b py-2 hover:bg-gray-50 hover:cursor-pointer'
                           >
                              <p className='text-sm text-gray-500'>
                                 {chat.user_query ?? 'No message'}
                              </p>
                              <p className='text-right text-[12px] text-gray-500'>
                                 {format(new Date(chat.updated_at), 'hh:mm a')}
                              </p>
                           </div>
                        ))}
                     </div>
                  </div>
               );
            })}
         </div>
      );
   };

   const renderHistoryByAgent = () => {
      if (currentAgent === 'alerting-agent') {
         return currentHistory['alerting-agent']?.length ? (
            <div className='h-[calc(100vh-150px)] overflow-auto pb-[300px]'>
               {renderAlertHistory()}
            </div>
         ) : (
            noHistoryUI()
         );
      } else if (currentAgent === 'analytics-agent') {
         return currentHistory['analytics-agent']?.length ? (
            <div className='h-[calc(100vh-150px)] overflow-auto pb-[300px]'>
               {renderAnalyticsHistory()}
            </div>
         ) : (
            noHistoryUI()
         );
      } else if (currentAgent === 'meta-ads-manager-auto') {
         return metaAdsAutoAgentHistoryData?.length ? (
            <div className='h-[calc(100vh-150px)] overflow-auto'>
               {renderMetaAdsAutoAgentHistory()}
            </div>
         ) : (
            noHistoryUI()
         );
      }
      return null;
   };

   const noHistoryUI = () => (
      <div className='h-[calc(100vh-150px)] overflow-auto'>
         <div className='text-center text-gray-500'>
            No history available. Start chatting to see history.
         </div>
      </div>
   );

   useEffect(() => {
      if (
         !isAlertsHistoryLoading &&
         alertsHistoryData &&
         alertsHistoryData.length > 0
      ) {
         dispatch(
            setCurrentHistory({
               agent: 'alerting-agent',
               history: alertsHistoryData,
            }),
         );
      }
   }, [isAlertsHistoryLoading, alertsHistoryData, dispatch]);

   useEffect(() => {
      if (
         !isAnalyticsHistoryLoading &&
         analyticsHistoryData &&
         analyticsHistoryData.length > 0
      ) {
         dispatch(
            setCurrentHistory({
               agent: 'analytics-agent',
               history: analyticsHistoryData,
            }),
         );
      }
   }, [isAnalyticsHistoryLoading]);

   useEffect(() => {
      setSearchTerm('');
   }, [currentAgent]);

   return (
      <Popover>
         <PopoverTrigger>
            <Button className='hover:cursor-pointer' variant='outline'>
               <LuHistory />
            </Button>
         </PopoverTrigger>
         <PopoverContent className='w-[300px] sm:w-[350px] h-[calc(100vh-150px)] mr-2 overflow-hidden bg-white'>
            <div className='grid gap-4'>
               <div className='space-y-2'>
                  <p className='text-xl font-bold'>History</p>
               </div>
               <div className='space-y-2'>
                  <Input
                     placeholder='Search history...'
                     value={searchTerm}
                     onChange={(e) => setSearchTerm(e.target.value)}
                  />
               </div>
               {renderHistoryByAgent()}
            </div>
         </PopoverContent>
      </Popover>
   );
};

export default MarcoHistoryPopover;
