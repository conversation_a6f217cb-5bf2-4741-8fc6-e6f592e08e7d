import { Box } from '@chakra-ui/react';
import { openModal } from '@/store/reducer/modal-reducer';
import { modalTypes } from '@/components/modals/modal-types';
import { useDispatch } from 'react-redux';
import TooltipContent from '../../dashboard/components/tooltip-content';
import { GrCircleQuestion } from 'react-icons/gr';
import { FiPlusCircle } from 'react-icons/fi';
import KPICard from './KpiCard';
import {
   DashboardDataCardProps,
   KpiDataWithMeta,
   KPIMeta,
} from '../dashboard-types';
import { BsPinFill } from 'react-icons/bs';
import KPIImage from '@/pages/dashboard/components/kpi-image';
import { filterVisibleKPI } from './helper';

const DashboardDataCard = ({
   keyValue,
   label,
   data,
   connectorKey,
   metaData,
   anomalies,
}: DashboardDataCardProps) => {
   const dispatch = useDispatch();
   const { filteredCat, filteredMeta } = filterVisibleKPI(
      keyValue === 'pinned',
      data[connectorKey],
      metaData as KPIMeta[],
      connectorKey,
   );

   const imageStyles: object = {
      height: keyValue == 'amazon_ads' ? '20px' : '25px',
      width: keyValue == 'amazon_ads' ? '30px' : '25px',
      position: keyValue == 'amazon_ads' ? 'relative' : '',
      top: keyValue == 'amazon_ads' ? '3px' : '',
   };

   if (
      data[connectorKey] === undefined ||
      Object.keys(data[connectorKey]).length === 0
   ) {
      return (
         <Box>
            <div className='flex gap-2 items-center mb-4 font-medium head5'>
               {keyValue === 'pinned' ? (
                  <BsPinFill />
               ) : (
                  <KPIImage kpiCat={keyValue} style={imageStyles} />
               )}{' '}
               {label}
            </div>
            <p className='para5 font-normal text-charcoal pt-3'>
               No data available for selected date range.
            </p>
         </Box>
      );
   }

   const handleAdd = () => {
      dispatch(
         openModal({
            modalType: modalTypes.ADD_KPI_MODAL2,
            modalProps: {
               metaData: filteredMeta,
               head: connectorKey,
            },
         }),
      );
   };

   console.log('filteredCat', anomalies);

   return (
      <Box>
         <div className=' flex items-center gap-2 mb-4 font-medium head5'>
            {keyValue === 'pinned' ? (
               <BsPinFill />
            ) : (
               <KPIImage kpiCat={keyValue} style={imageStyles} />
            )}{' '}
            {label}
            <TooltipContent category={connectorKey} hasArrow placement='top'>
               <GrCircleQuestion />
            </TooltipContent>
            <TooltipContent label='Add more KPIs' hasArrow placement='top'>
               <FiPlusCircle cursor={'pointer'} onClick={handleAdd} />{' '}
            </TooltipContent>
         </div>

         <Box display='flex' flexWrap='wrap' gap={4}>
            {Object.values(filteredCat)
               .sort((a, b) => a.order - b.order)
               .map((kpi: KpiDataWithMeta) => (
                  <KPICard
                     key={kpi.kpi_names}
                     keyValue={keyValue}
                     kpiDetails={kpi}
                     anomaly={anomalies ? anomalies[kpi.kpi_names] : null}
                  />
               ))}
         </Box>
      </Box>
   );
};

export default DashboardDataCard;
