import { KPIStoreRange } from '@/pages/dashboard/utils/interface';
import {
   ConnectorKpiData,
   ConnectorKpiDataWithMeta,
   KPIMeta,
   KpiTimelineItem,
} from '../dashboard-types';

export const filterVisibleKPI = (
   isPinned: boolean,
   catData: ConnectorKpiData, // your data from backend
   metaData: KPIMeta[],
   category: string,
): { filteredCat: ConnectorKpiDataWithMeta; filteredMeta: KPIMeta[] } => {
   const filteredCat: ConnectorKpiDataWithMeta = {};

   const availableKPI = metaData.filter((kpi: KPIMeta) => {
      return isPinned ? kpi.pinned : kpi.visible;
   });

   const filteredMeta = metaData.filter((kpi) => {
      if (kpi.category !== category) return true;
      return !!catData[kpi.kpi];
   });

   availableKPI.forEach((kpi) => {
      if (
         catData[kpi.kpi] &&
         catData[kpi.kpi].current_allData?.length > 0 &&
         catData[kpi.kpi].category == kpi.category
      ) {
         filteredCat[kpi.kpi] = {
            ...(catData[kpi.kpi] || kpi),
            visible: kpi.visible ?? true,
            pinned: kpi.pinned ?? true,
            order: isPinned
               ? (kpi.pinned_order ?? 999)
               : (kpi.visible_order ?? 999),
         };
      }
   });

   // availableKPI.forEach((kpi: KPIMeta) => {
   //    if (catData[kpi.kpi] || isPinned) {
   //       filteredCat[kpi.kpi] = {
   //          ...(catData[kpi.kpi] || kpi),
   //          visible: kpi.visible ?? true,
   //          pinned: kpi.pinned ?? true,
   //          order: isPinned
   //             ? (kpi.pinned_order ?? 999)
   //             : (kpi.visible_order ?? 999),
   //       };
   //    }
   // });

   return { filteredCat, filteredMeta };
};

export const getFullData = (
   dateRange: KPIStoreRange,
   data: KpiTimelineItem[],
   // groupBy: string,
): KpiTimelineItem[] => {
   // if (groupBy !== 'day') return data;

   const startDate = new Date(dateRange.start);
   const endDate = new Date(dateRange.end);

   const dateArray: KpiTimelineItem[] = [];
   let i = 0;

   while (startDate.getTime() <= endDate.getTime()) {
      const currData = data[i] || dateArray[0];
      const dataD = new Date(currData.date);

      if (
         dataD.getFullYear() === startDate.getFullYear() &&
         dataD.getMonth() === startDate.getMonth() &&
         dataD.getDate() === startDate.getDate()
      ) {
         dateArray.push(currData);
         i++;
      } else {
         dateArray.push({
            ...currData,
            date: `${startDate.getFullYear()}-${startDate.getMonth() + 1}-${startDate.getDate()}`,
            kpi_value: 0,
         });
      }

      startDate.setDate(startDate.getDate() + 1);
   }

   return dateArray;
};
